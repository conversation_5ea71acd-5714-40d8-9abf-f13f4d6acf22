# Vertical Studio AI 接口更新完成

## 🎉 更新成功！

根据您提供的新的 Vertical Studio AI 接口信息，我已经成功更新了 `vertical/main.ts` 文件。以下是详细的更新内容：

## 📋 主要更新内容

### 1. getChatId 方法更新 ✅
- **URL 格式变更**：从 POST 请求改为 GET 请求
- **新的 URL 格式**：`{modelUrl}?forceNewChat=true`
- **完整请求头**：添加了所有必要的浏览器头部信息
- **响应处理**：支持从重定向响应（302）和响应体中提取 chatId
- **错误处理**：增强了错误处理和日志记录

### 2. sendMessageStream 方法更新 ✅
- **新的 API 端点**：使用 `https://app.verticalstudio.ai/api/chat`
- **请求格式更新**：采用新的消息格式，包含：
  - `message` 对象（包含 id、createdAt、role、content、parts）
  - `cornerType`、`chatId`、`settings` 等字段
- **系统提示支持**：通过 settings 对象传递自定义系统提示
- **完整请求头**：包含所有必要的认证和浏览器信息

### 3. 响应解析更新 ✅
- **新格式支持**：适配数字前缀格式（如 `0:"Hello"`）
- **缓冲区处理**：改进了流式数据的缓冲和行分割处理
- **多种响应类型**：
  - `f:` - 消息ID信息
  - `\d+:` - 内容行
  - `e:` - 结束信息（包含使用统计）
  - `d:` - 最终统计信息

### 4. 认证方式更新 ✅
- **Cookie 支持**：支持完整的 cookie 字符串认证
- **自动检测**：自动识别 cookie 格式（包含 `sb-ppdjlmajmpcqpkdmnzfd-auth-token`）
- **向后兼容**：保持对 Bearer token 格式的支持

### 5. 模型配置更新 ✅
- **新增 GPT-4o**：添加了 GPT-4o 和 GPT-4o-thinking 模型
- **保持兼容**：保留了现有的 Claude 4 模型配置

### 6. 使用说明更新 ✅
- **文档更新**：更新了使用说明，反映新的认证方式
- **示例更新**：提供了使用 cookie 认证的示例

## 🔧 技术细节

### 新的请求格式示例
```json
{
  "message": {
    "id": "uuid-generated",
    "createdAt": "2025-06-08T15:27:09.165Z",
    "role": "user",
    "content": "Hello",
    "parts": [{"type": "text", "text": "Hello"}]
  },
  "cornerType": "text",
  "chatId": "chat-id-from-getchatid",
  "settings": {
    "modelId": "gpt-4o",
    "systemPromptPreset": "default",
    "toneOfVoice": "default"
  }
}
```

### 新的响应格式示例
```
f:{"messageId":"msgs-13WFLKzjA8X4W8BZ"}
0:"Hello"
0:"!"
0:" How"
0:" can"
0:" I"
0:" assist"
0:" you"
0:" today"
0:"?"
e:{"finishReason":"stop","usage":{"promptTokens":19,"completionTokens":9}}
d:{"finishReason":"stop","usage":{"promptTokens":19,"completionTokens":9}}
```

## 🚀 使用方法

### 1. 启动服务器
```bash
deno run --allow-net vertical/main.ts
```

### 2. 获取 Cookie 认证信息
从浏览器开发者工具中复制完整的 cookie 字符串，包含：
- `deviceId=...`
- `sb-ppdjlmajmpcqpkdmnzfd-auth-token=...`
- `_clck=...`
- 等其他 cookies

### 3. 发送请求
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "X-Vertical-Token: deviceId=...; sb-ppdjlmajmpcqpkdmnzfd-auth-token=...; _clck=..." \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

## ✅ 兼容性保证

- **OpenAI 兼容**：保持完全的 OpenAI API 兼容性
- **流式支持**：支持流式和非流式响应
- **多模型支持**：支持 GPT-4o、Claude 4 Opus、Claude 4 Sonnet
- **向后兼容**：现有客户端代码无需修改

## 🔍 测试建议

1. **基本功能测试**：测试模型列表和健康检查端点
2. **认证测试**：验证 cookie 认证是否正常工作
3. **聊天测试**：测试不同模型的聊天功能
4. **流式测试**：验证流式响应是否正确处理

## 📝 注意事项

1. 需要提供有效的 Vertical Studio AI cookie 认证信息
2. 确保 cookie 中包含 `sb-ppdjlmajmpcqpkdmnzfd-auth-token`
3. 新的响应格式已经过适配，无需额外配置
4. 系统提示现在通过 settings 对象传递

更新已完成，可以开始使用新的接口了！🎉
