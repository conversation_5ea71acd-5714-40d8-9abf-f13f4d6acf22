#!/usr/bin/env -S deno run --allow-net

/**
 * Vertical OpenAI Compatible API - Deno Single File Implementation
 *
 * 这是一个完全自包含的 Deno 服务，将 Vertical API 转换为 OpenAI 兼容的 API 格式。
 *
 * 使用方法:
 * 1. 修改下面的 MODELS_DATA 配置
 * 2. 运行: deno run --allow-net main.ts
 * 3. 发送请求时需要两个认证头:
 *    - Authorization: Bearer <client-api-key>  (用于客户端认证)
 *    - X-Vertical-Token: <vertical-cookies>    (用于 Vertical API 调用，完整的 cookie 字符串)
 *
 * 示例请求:
 * curl -X POST http://localhost:8000/v1/chat/completions \
 *   -H "Authorization: Bearer sk-your-custom-key-here" \
 *   -H "X-Vertical-Token: deviceId=...; sb-ppdjlmajmpcqpkdmnzfd-auth-token=...; _clck=..." \
 *   -H "Content-Type: application/json" \
 *   -d '{"model": "gpt-4o", "messages": [{"role": "user", "content": "Hello!"}]}'
 */

import { Application, Router, Context } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { crypto } from "https://deno.land/std@0.208.0/crypto/mod.ts";

// ===== 配置常量 =====
const CONVERSATION_CACHE_MAX_SIZE = 100;
const DEFAULT_REQUEST_TIMEOUT = 30000;
const PORT = 8000;

// ===== 内置配置 =====
// 根据新的 Vertical Studio AI 接口格式更新
const MODELS_DATA = {
  "data": [
    {
      "id": "gpt-4o",
      "object": "model",
      "created": 1703980800,
      "owned_by": "vertical-studio",
      "vertical_model_id": "gpt-4o",
      "vertical_model_url": "https://app.verticalstudio.ai/stream/models/gpt-4o.data",
      "output_reasoning_flag": false,
      "description": "GPT-4o (final answer only)"
    },
    {
      "id": "gpt-4o-thinking",
      "object": "model",
      "created": 1703980800,
      "owned_by": "vertical-studio",
      "vertical_model_id": "gpt-4o",
      "vertical_model_url": "https://app.verticalstudio.ai/stream/models/gpt-4o.data",
      "output_reasoning_flag": true,
      "description": "GPT-4o (with thinking steps)"
    },
    {
      "id": "claude-4-opus-20250514",
      "object": "model",
      "created": 1703980800,
      "owned_by": "vertical-studio",
      "vertical_model_id": "claude-4-opus-20250514",
      "vertical_model_url": "https://app.verticalstudio.ai/stream/models/claude-4-opus.data",
      "output_reasoning_flag": false,
      "description": "Claude 4 Opus (final answer only)"
    },
    {
      "id": "claude-4-opus-20250514-thinking",
      "object": "model",
      "created": 1703980800,
      "owned_by": "vertical-studio",
      "vertical_model_id": "claude-4-opus-20250514",
      "vertical_model_url": "https://app.verticalstudio.ai/stream/models/claude-4-opus.data",
      "output_reasoning_flag": true,
      "description": "Claude 4 Opus (with thinking steps)"
    },
    {
      "id": "claude-4-sonnet-20250514",
      "object": "model",
      "created": 1703980800,
      "owned_by": "vertical-studio",
      "vertical_model_id": "claude-4-sonnet-20250514",
      "vertical_model_url": "https://app.verticalstudio.ai/stream/models/claude-4-sonnet.data",
      "output_reasoning_flag": false,
      "description": "Claude 4 Sonnet (final answer only)"
    },
    {
      "id": "claude-4-sonnet-20250514-thinking",
      "object": "model",
      "created": 1703980800,
      "owned_by": "vertical-studio",
      "vertical_model_id": "claude-4-sonnet-20250514",
      "vertical_model_url": "https://app.verticalstudio.ai/stream/models/claude-4-sonnet.data",
      "output_reasoning_flag": true,
      "description": "Claude 4 Sonnet (with thinking steps)"
    }
  ]
};

// ===== 全局变量 =====
let conversationCache: Map<string, any> = new Map();

// ===== 类型定义 =====
interface ChatMessage {
  role: string;
  content: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

interface ModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  vertical_model_id?: string;
  vertical_model_url?: string;
  output_reasoning_flag?: boolean;
  description?: string;
}

interface StreamChoice {
  delta: Record<string, any>;
  index: number;
  finish_reason?: string;
}

interface StreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: StreamChoice[];
}

interface ChatCompletionChoice {
  message: ChatMessage;
  index: number;
  finish_reason: string;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// ===== 工具函数 =====
function generateUUID(): string {
  return crypto.randomUUID();
}

function getCurrentTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

async function sha256Hash(text: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
}

async function generateMessageFingerprint(role: string, content: string): Promise<string> {
  const hash = await sha256Hash(content);
  return `${role}:${hash}`;
}

// ===== 日志工具函数 =====
function formatTimestamp(): string {
  return new Date().toISOString();
}

function logInfo(requestId: string, step: string, message: string, data?: any): void {
  const timestamp = formatTimestamp();
  const logData = data ? ` | 数据: ${JSON.stringify(data, null, 2)}` : '';
  console.log(`[${timestamp}] [INFO] [${requestId}] [${step}] ${message}${logData}`);
}

function logError(requestId: string, step: string, message: string, error?: any): void {
  const timestamp = formatTimestamp();
  const errorData = error ? ` | 错误: ${error instanceof Error ? error.message : JSON.stringify(error)}` : '';
  console.error(`[${timestamp}] [ERROR] [${requestId}] [${step}] ${message}${errorData}`);
}

function logWarning(requestId: string, step: string, message: string, data?: any): void {
  const timestamp = formatTimestamp();
  const logData = data ? ` | 数据: ${JSON.stringify(data, null, 2)}` : '';
  console.warn(`[${timestamp}] [WARN] [${requestId}] [${step}] ${message}${logData}`);
}

// ===== 工具函数 =====

function getModelItem(modelId: string): ModelInfo | null {
  for (const model of MODELS_DATA.data) {
    if (model.id === modelId) {
      return model;
    }
  }
  return null;
}

function extractVerticalAuthToken(authHeader: string | undefined): string | null {
  // 从 X-Vertical-Token 头或 Authorization 头中提取 Vertical 令牌
  if (!authHeader) return null;

  // 如果是完整的 cookie 字符串，直接返回
  if (authHeader.includes('sb-ppdjlmajmpcqpkdmnzfd-auth-token=') ||
      authHeader.includes('deviceId=') ||
      authHeader.includes('_clck=')) {
    return authHeader;
  }

  // 支持 Bearer 格式: "Bearer vertical-token"
  const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/i);
  if (bearerMatch) {
    return bearerMatch[1];
  }

  // 直接返回令牌
  return authHeader;
}

// ===== 认证中间件 =====
function extractBearerToken(authHeader: string | undefined): string | null {
  if (!authHeader) return null;
  const match = authHeader.match(/^Bearer\s+(.+)$/i);
  return match ? match[1] : null;
}

async function authenticateClient(ctx: Context, next: () => Promise<unknown>): Promise<void> {
  const requestId = generateUUID().substring(0, 8);
  const authHeader = ctx.request.headers.get("Authorization");

  logInfo(requestId, "AUTH", "开始客户端认证", {
    method: ctx.request.method,
    url: ctx.request.url.pathname,
    hasAuthHeader: !!authHeader,
    authHeaderPrefix: authHeader ? authHeader.substring(0, 20) + "..." : null
  });

  const token = extractBearerToken(authHeader);

  if (!token) {
    logError(requestId, "AUTH", "认证失败：缺少有效的Bearer token", {
      authHeader: authHeader ? authHeader.substring(0, 50) + "..." : null
    });
    ctx.response.status = 401;
    ctx.response.headers.set("WWW-Authenticate", "Bearer");
    ctx.response.body = { detail: "需要在 Authorization header 中提供 API 密钥" };
    return;
  }

  logInfo(requestId, "AUTH", "客户端认证成功", {
    tokenPrefix: token.substring(0, 10) + "..."
  });

  // 将 requestId 传递给后续处理
  (ctx as any).requestId = requestId;

  await next();
}

// ===== VerticalApiClient 实现 =====
class VerticalApiClient {
  constructor() {}

  async getChatId(modelUrl: string, authToken: string, requestId?: string): Promise<string | null> {
    const logId = requestId || "UNKNOWN";

    try {
      logInfo(logId, "CHAT_ID", "开始获取chat_id", {
        modelUrl,
        tokenPrefix: authToken.substring(0, 10) + "..."
      });

      // 构造新的 URL 格式：添加 ?forceNewChat=true 参数
      const newChatUrl = `${modelUrl}?forceNewChat=true`;

      logInfo(logId, "CHAT_ID", "发送GET请求到Vertical API", {
        url: newChatUrl,
        method: "GET"
      });

      const response = await fetch(newChatUrl, {
        method: "GET",
        // "Cookie": "sb-ppdjlmajmpcqpkdmnzfd-auth-token="+authToken, // 使用完整的 cookie 字符串
        headers: {
          "accept": "*/*",
          "accept-language": "zh-CN,zh;q=0.9",
          "baggage": "sentry-environment=production,sentry-public_key=0fb63d9554f3efa0c9fa41330d426e6a,sentry-trace_id=d06f91b597ee453f9e7c54d481037662,sentry-sample_rate=1,sentry-sampled=true",
          "cookie": "sb-ppdjlmajmpcqpkdmnzfd-auth-token=base64-**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;",
          "priority": "u=1, i",
          "referer": "https://app.verticalstudio.ai/",
          "sec-ch-ua": '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          "sentry-trace": "d06f91b597ee453f9e7c54d481037662-b6824dd09bb237be-1",
          "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
        }
      });

      logInfo(logId, "CHAT_ID", "收到Vertical API响应", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      // 检查是否是重定向响应 (302)
      if (response.status === 302 || response.headers.get('location')) {
        const location = response.headers.get('location');
        if (location) {
          // 从重定向 URL 中提取 chatId
          // URL 格式: https://app.verticalstudio.ai/stream/models/gpt-4o/cmbnsberq0x4i9a0wpvkaoiv0
          const chatIdMatch = location.match(/\/([a-zA-Z0-9]+)$/);
          const chatId = chatIdMatch ? chatIdMatch[1] : null;

          logInfo(logId, "CHAT_ID", "从重定向URL提取chat_id", {
            location,
            extractedChatId: chatId
          });

          return chatId;
        }
      }

      // 如果不是重定向，尝试解析响应体
      const responseText = await response.text();
      logInfo(logId, "CHAT_ID", "解析响应文本", {
        responseLength: responseText.length,
        responsePreview: responseText.substring(0, 200)
      });

      // 尝试从响应中提取 chatId
      // 响应格式可能包含类似 ["SingleFetchRedirect",1],{"_2":3,"_4":5,"_6":7,"_8":7,"_9":7},"redirect","https://app.verticalstudio.ai/stream/models/gpt-4o/cmbnsberq0x4i9a0wpvkaoiv0"
      const urlMatch = responseText.match(/https:\/\/app\.verticalstudio\.ai\/stream\/models\/[^\/]+\/([a-zA-Z0-9]+)/);
      const chatId = urlMatch ? urlMatch[1] : null;

      logInfo(logId, "CHAT_ID", "成功解析chat_id", {
        extractedChatId: chatId
      });

      return chatId;
    } catch (error) {
      logError(logId, "CHAT_ID", "获取chat_id时发生异常", error);
      return null;
    }
  }

  async* sendMessageStream(
    authToken: string,
    chatId: string,
    message: string,
    modelId: string,
    outputReasoning: boolean,
    systemPrompt: string,
    requestId?: string
  ): AsyncGenerator<string, void, unknown> {
    const logId = requestId || "UNKNOWN";

    try {
      // 构造新的请求格式
      const messageId = generateUUID();
      const payload = {
        message: {
          id: messageId,
          createdAt: new Date().toISOString(),
          role: "user",
          content: message,
          parts: [{ type: "text", text: message }]
        },
        cornerType: "text",
        chatId: chatId,
        settings: {
          modelId: modelId,
          systemPromptPreset: systemPrompt ? "custom" : "default",
          toneOfVoice: "default"
        }
      };

      // 如果有自定义系统提示，添加到 settings 中
      if (systemPrompt) {
        (payload.settings as any).systemPrompt = systemPrompt;
      }

      logInfo(logId, "STREAM", "开始发送流式消息", {
        chatId,
        modelId,
        messageLength: message.length,
        systemPromptLength: systemPrompt.length,
        outputReasoning,
        tokenPrefix: authToken.substring(0, 10) + "..."
      });

      logInfo(logId, "STREAM", "构造请求载荷", {
        payload: {
          ...payload,
          message: {
            ...payload.message,
            content: message.length > 100 ? message.substring(0, 100) + "..." : message
          }
        }
      });

      // 使用新的 API 端点
      const apiUrl = "https://app.verticalstudio.ai/api/chat";

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "accept": "*/*",
          "accept-language": "zh-CN,zh;q=0.9",
          "baggage": "sentry-environment=production,sentry-public_key=0fb63d9554f3efa0c9fa41330d426e6a,sentry-trace_id=d06f91b597ee453f9e7c54d481037662,sentry-sample_rate=1,sentry-sampled=true",
          "content-type": "application/json",
          "cookie": authToken, // 使用完整的 cookie 字符串
          "origin": "https://app.verticalstudio.ai",
          "priority": "u=1, i",
          "referer": `https://app.verticalstudio.ai/stream/models/${modelId}/${chatId}`,
          "sec-ch-ua": '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          "sentry-trace": "d06f91b597ee453f9e7c54d481037662-b6824dd09bb237be-1",
          "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
        },
        body: JSON.stringify(payload)
      });

      logInfo(logId, "STREAM", "收到流式响应", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        hasBody: !!response.body,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        logError(logId, "STREAM", "流式请求失败", {
          status: response.status,
          statusText: response.statusText
        });
        yield `error:${JSON.stringify({ message: `HTTP ${response.status}: ${response.statusText}` })}`;
        return;
      }

      if (!response.body) {
        logError(logId, "STREAM", "响应体为空");
        yield `error:${JSON.stringify({ message: "No response body" })}`;
        return;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let chunkCount = 0;
      let buffer = "";

      logInfo(logId, "STREAM", "开始读取流式数据");

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            logInfo(logId, "STREAM", "流式数据读取完成", { totalChunks: chunkCount });
            break;
          }

          chunkCount++;
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 按行分割处理
          const lines = buffer.split('\n');
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || "";

          logInfo(logId, "STREAM", `处理第${chunkCount}个数据块`, {
            chunkSize: chunk.length,
            linesCount: lines.length,
            chunkPreview: chunk.length > 200 ? chunk.substring(0, 200) + "..." : chunk
          });

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine) {
              // 处理新的响应格式
              if (trimmedLine.startsWith('f:')) {
                // 消息ID信息，可以忽略或记录
                logInfo(logId, "STREAM", "收到消息ID", { line: trimmedLine });
              } else if (trimmedLine.match(/^\d+:/)) {
                // 内容行，格式如 0:"Hello"
                yield trimmedLine;
              } else if (trimmedLine.startsWith('e:')) {
                // 结束信息，包含使用统计
                logInfo(logId, "STREAM", "收到结束信息", { line: trimmedLine });
                yield `d:${trimmedLine.substring(2)}`; // 转换为旧格式
              } else if (trimmedLine.startsWith('d:')) {
                // 最终统计信息
                logInfo(logId, "STREAM", "收到最终统计", { line: trimmedLine });
                yield trimmedLine;
              } else {
                // 其他格式的行
                logInfo(logId, "STREAM", "收到其他格式行", { line: trimmedLine });
                yield trimmedLine;
              }
            }
          }
        }

        // 处理缓冲区中剩余的内容
        if (buffer.trim()) {
          const trimmedLine = buffer.trim();
          if (trimmedLine.match(/^\d+:/)) {
            yield trimmedLine;
          }
        }
      } finally {
        reader.releaseLock();
        logInfo(logId, "STREAM", "释放流式读取器");
      }
    } catch (error) {
      logError(logId, "STREAM", "流式处理发生异常", error);
      yield `error:${JSON.stringify({ message: `Network error: ${error.message}` })}`;
    }
  }
}

// ===== JSON 解析工具函数 =====
function parseJsonStringContent(line: string, prefixLen: number, suffixLen: number): string {
  const contentSegment = line.substring(prefixLen, line.length + suffixLen);
  try {
    // 将提取的片段视为JSON字符串的值进行解析，以处理所有标准转义序列
    return JSON.parse(`"${contentSegment}"`);
  } catch (error) {
    // 如果解析失败，回退到手动替换
    console.warn(`警告: parseJsonStringContent 中 JSONDecodeError，回退处理。原始片段: ${contentSegment.substring(0, 100)}...`);
    let tempContent = contentSegment.replace(/\\"/g, '"'); // 处理 \" -> "
    tempContent = tempContent.replace(/\\n/g, '\n');       // 处理 \n -> 换行符
    tempContent = tempContent.replace(/\\t/g, '\t');       // 处理 \t -> 制表符
    return tempContent;
  }
}

// ===== 对话缓存管理 =====
function updateConversationCache(
  isNewCachedConv: boolean,
  verticalChatIdForCache: string,
  matchedConvIdForCacheUpdate: string | null,
  originalRequestMessages: ChatMessage[],
  fullAssistantReplyStr: string,
  systemPromptHashForCache: number,
  modelUrlForCache: string
): void {
  if (isNewCachedConv) {
    // 新对话，创建缓存条目
    const newInternalId = generateUUID();
    const currentFingerprints: Promise<string>[] = originalRequestMessages.map(msg =>
      generateMessageFingerprint(msg.role, msg.content)
    );

    // 添加助手回复的指纹
    currentFingerprints.push(generateMessageFingerprint("assistant", fullAssistantReplyStr));

    Promise.all(currentFingerprints).then(fingerprints => {
      conversationCache.set(newInternalId, {
        vertical_chat_id: verticalChatIdForCache,
        vertical_model_url: modelUrlForCache,
        system_prompt_hash: systemPromptHashForCache,
        message_fingerprints: fingerprints,
        last_seen: Date.now()
      });

      // LRU 驱逐
      if (conversationCache.size > CONVERSATION_CACHE_MAX_SIZE) {
        const firstKey = conversationCache.keys().next().value;
        conversationCache.delete(firstKey);
      }
    });
  } else if (matchedConvIdForCacheUpdate) {
    // 更新现有对话
    const cachedItem = conversationCache.get(matchedConvIdForCacheUpdate);
    if (cachedItem) {
      // 添加最新用户消息的指纹（如果还未添加）
      if (originalRequestMessages.length > 0) {
        const lastUserMsg = originalRequestMessages[originalRequestMessages.length - 1];
        generateMessageFingerprint(lastUserMsg.role, lastUserMsg.content).then(lastUserFingerprint => {
          // 检查是否已经存在，避免重复
          if (!cachedItem.message_fingerprints ||
              cachedItem.message_fingerprints[cachedItem.message_fingerprints.length - 1] !== lastUserFingerprint) {
            cachedItem.message_fingerprints.push(lastUserFingerprint);
          }

          // 添加助手回复的指纹
          generateMessageFingerprint("assistant", fullAssistantReplyStr).then(assistantFingerprint => {
            cachedItem.message_fingerprints.push(assistantFingerprint);
            cachedItem.last_seen = Date.now();
          });
        });
      }
    }
  }
}

// ===== 流式响应适配器 =====
async function* openaiStreamAdapter(
  apiStreamGenerator: AsyncGenerator<string, void, unknown>,
  modelNameForResponse: string,
  reasoningRequested: boolean,
  verticalChatIdForCache: string,
  isNewCachedConv: boolean,
  matchedConvIdForCacheUpdate: string | null,
  originalRequestMessages: ChatMessage[],
  systemPromptHashForCache: number,
  modelUrlForCache: string,
  requestId?: string
): AsyncGenerator<string, void, unknown> {
  const logId = requestId || "UNKNOWN";
  const fullAssistantReplyParts: string[] = [];
  const streamId = `chatcmpl-${generateUUID().replace(/-/g, '')}`;

  logInfo(logId, "ADAPTER", "开始流式适配器处理", {
    streamId,
    modelName: modelNameForResponse,
    reasoningRequested,
    chatId: verticalChatIdForCache
  });

  try {
    let firstChunkSent = false;
    let lineCount = 0;

    for await (const line of apiStreamGenerator) {
      lineCount++;
      if (line.startsWith("error:")) {
        // 处理错误
        try {
          const errorData = JSON.parse(line.substring(6));
          const errorMsg = errorData.message || "Unknown error";

          const errorResp: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created: getCurrentTimestamp(),
            model: modelNameForResponse,
            choices: [{
              delta: { role: "assistant", content: `错误: ${errorMsg}` },
              index: 0,
              finish_reason: "stop"
            }]
          };
          yield `data: ${JSON.stringify(errorResp)}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        } catch {
          const errorMsg = "Unknown error from Vertical API";
          const errorResp: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created: getCurrentTimestamp(),
            model: modelNameForResponse,
            choices: [{
              delta: { role: "assistant", content: `错误: ${errorMsg}` },
              index: 0,
              finish_reason: "stop"
            }]
          };
          yield `data: ${JSON.stringify(errorResp)}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        }
      }

      let deltaPayload: Record<string, any> | null = null;

      // 解析新的 Vertical API 响应格式
      if (line.match(/^\d+:".*"$/)) {
        // 新格式的内容行，如 0:"Hello"
        const colonIndex = line.indexOf(':');
        const contentWithQuotes = line.substring(colonIndex + 1);
        const finalContent = parseJsonStringContent(contentWithQuotes, 1, -1);

        logInfo(logId, "ADAPTER", `处理主要内容 (第${lineCount}行)`, {
          contentLength: finalContent.length,
          contentPreview: finalContent.length > 100 ? finalContent.substring(0, 100) + "..." : finalContent,
          isFirstChunk: !firstChunkSent
        });

        if (!firstChunkSent) {
          deltaPayload = { role: "assistant", content: finalContent };
        } else {
          deltaPayload = { content: finalContent };
        }
        fullAssistantReplyParts.push(finalContent);
      } else if (reasoningRequested && line.startsWith('g:"') && line.endsWith('"')) {
        // 推理内容（仅当请求时才包含）
        const thinkingContent = parseJsonStringContent(line, 3, -1);
        logInfo(logId, "ADAPTER", `处理推理内容 (第${lineCount}行)`, {
          contentLength: thinkingContent.length,
          contentPreview: thinkingContent.length > 100 ? thinkingContent.substring(0, 100) + "..." : thinkingContent,
          isFirstChunk: !firstChunkSent
        });

        // 为缓存添加带前缀的思考内容，但在SSE事件中分离reasoning_content
        fullAssistantReplyParts.push(`[Thinking]: ${thinkingContent}`);
        if (!firstChunkSent) {
          deltaPayload = { role: "assistant", reasoning_content: thinkingContent };
        } else {
          deltaPayload = { reasoning_content: thinkingContent };
        }
      } else if (line.startsWith('d:')) {
        // 结束信号或统计信息
        logInfo(logId, "ADAPTER", `处理结束信号 (第${lineCount}行)`, { line });
        try {
          const eventData = JSON.parse(line.substring(2));
          if (eventData.finishReason === "stop" || eventData.type === "done" || eventData.type === "DONE") {
            logInfo(logId, "ADAPTER", "收到完成信号", { eventData });
            // 发送最终的 finish_reason
            const finalResp: StreamResponse = {
              id: streamId,
              object: "chat.completion.chunk",
              created: getCurrentTimestamp(),
              model: modelNameForResponse,
              choices: [{
                delta: {},
                index: 0,
                finish_reason: "stop"
              }]
            };
            yield `data: ${JSON.stringify(finalResp)}\n\n`;
            break;
          }
        } catch (parseError) {
          logWarning(logId, "ADAPTER", "解析结束信号失败", { line, error: parseError });
        }
      } else if (line.startsWith('e:')) {
        // 结束信息，包含使用统计
        logInfo(logId, "ADAPTER", `处理结束信息 (第${lineCount}行)`, { line });
        try {
          const eventData = JSON.parse(line.substring(2));
          if (eventData.finishReason === "stop") {
            logInfo(logId, "ADAPTER", "收到完成信号", { eventData });
            // 发送最终的 finish_reason
            const finalResp: StreamResponse = {
              id: streamId,
              object: "chat.completion.chunk",
              created: getCurrentTimestamp(),
              model: modelNameForResponse,
              choices: [{
                delta: {},
                index: 0,
                finish_reason: "stop"
              }]
            };
            yield `data: ${JSON.stringify(finalResp)}\n\n`;
            break;
          }
        } catch (parseError) {
          logWarning(logId, "ADAPTER", "解析结束信息失败", { line, error: parseError });
        }
      } else {
        // 未识别的行格式
        logWarning(logId, "ADAPTER", `未识别的行格式 (第${lineCount}行)`, {
          line: line.length > 200 ? line.substring(0, 200) + "..." : line,
          lineLength: line.length
        });
      }

      // 如果有内容要发送
      if (deltaPayload) {
        const streamResp: StreamResponse = {
          id: streamId,
          object: "chat.completion.chunk",
          created: getCurrentTimestamp(),
          model: modelNameForResponse,
          choices: [{
            delta: deltaPayload,
            index: 0
          }]
        };

        if (!firstChunkSent) {
          firstChunkSent = true;
        }

        yield `data: ${JSON.stringify(streamResp)}\n\n`;
      }
    }

    // 更新缓存
    const fullAssistantReply = fullAssistantReplyParts.join("\n");
    logInfo(logId, "ADAPTER", "更新对话缓存", {
      replyLength: fullAssistantReply.length,
      replyPartsCount: fullAssistantReplyParts.length,
      isNewConversation: isNewCachedConv,
      chatId: verticalChatIdForCache
    });

    updateConversationCache(
      isNewCachedConv,
      verticalChatIdForCache,
      matchedConvIdForCacheUpdate,
      originalRequestMessages,
      fullAssistantReply,
      systemPromptHashForCache,
      modelUrlForCache
    );

    // 发送结束标记
    logInfo(logId, "ADAPTER", "发送流式结束标记", { totalLines: lineCount });
    yield "data: [DONE]\n\n";

  } catch (error) {
    logError(logId, "ADAPTER", "流式适配器发生异常", error);
    const errorResp: StreamResponse = {
      id: streamId,
      object: "chat.completion.chunk",
      created: getCurrentTimestamp(),
      model: modelNameForResponse,
      choices: [{
        delta: { role: "assistant", content: `内部错误: ${error}` },
        index: 0,
        finish_reason: "stop"
      }]
    };
    yield `data: ${JSON.stringify(errorResp)}\n\n`;
    yield "data: [DONE]\n\n";
  }
}

// ===== 聚合流式响应用于非流式返回 =====
async function aggregateStreamForNonStreamResponse(
  openaiSseStream: AsyncGenerator<string, void, unknown>,
  modelName: string,
  requestId?: string
): Promise<ChatCompletionResponse> {
  const logId = requestId || "UNKNOWN";
  const contentParts: string[] = [];
  const reasoningParts: string[] = [];
  let lineCount = 0;

  logInfo(logId, "AGGREGATE", "开始聚合流式响应为非流式", { modelName });

  for await (const sseLine of openaiSseStream) {
    lineCount++;

    if (sseLine.startsWith("data: ") && sseLine !== "data: [DONE]\n\n") {
      try {
        const data = JSON.parse(sseLine.substring(6).trim());
        if (data.choices && data.choices.length > 0) {
          const delta = data.choices[0].delta || {};
          if (delta.content) {
            contentParts.push(delta.content);
            logInfo(logId, "AGGREGATE", `添加内容片段 (第${lineCount}行)`, {
              contentLength: delta.content.length,
              totalContentParts: contentParts.length
            });
          } else if (delta.reasoning_content) {
            reasoningParts.push(delta.reasoning_content);
            logInfo(logId, "AGGREGATE", `添加推理片段 (第${lineCount}行)`, {
              reasoningLength: delta.reasoning_content.length,
              totalReasoningParts: reasoningParts.length
            });
          }
        }
      } catch (parseError) {
        logWarning(logId, "AGGREGATE", `解析SSE行失败 (第${lineCount}行)`, {
          line: sseLine.length > 200 ? sseLine.substring(0, 200) + "..." : sseLine,
          error: parseError
        });
      }
    }
  }

  // 组合最终内容，如果有推理内容则添加
  const combinedParts: string[] = [];
  if (reasoningParts.length > 0) {
    logInfo(logId, "AGGREGATE", "添加推理内容到最终响应", {
      reasoningPartsCount: reasoningParts.length
    });
    for (const part of reasoningParts) {
      combinedParts.push(`[Thinking]: ${part}`);
    }
  }

  combinedParts.push(...contentParts);
  const fullContent = combinedParts.join("");

  logInfo(logId, "AGGREGATE", "聚合完成", {
    totalLines: lineCount,
    contentPartsCount: contentParts.length,
    reasoningPartsCount: reasoningParts.length,
    finalContentLength: fullContent.length,
    contentPreview: fullContent.length > 200 ? fullContent.substring(0, 200) + "..." : fullContent
  });

  const responseId = `chatcmpl-${generateUUID().replace(/-/g, '')}`;

  return {
    id: responseId,
    object: "chat.completion",
    created: getCurrentTimestamp(),
    model: modelName,
    choices: [{
      message: { role: "assistant", content: fullContent },
      index: 0,
      finish_reason: "stop"
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}

// ===== 路由设置 =====
const router = new Router();
const verticalApiClient = new VerticalApiClient();

// 模型列表端点
router.get("/v1/models", authenticateClient, (ctx: Context) => {
  const modelList: ModelInfo[] = [];
  for (const model of MODELS_DATA.data) {
    modelList.push({
      id: model.id || "",
      object: "model",
      created: model.created || getCurrentTimestamp(),
      owned_by: model.owned_by || "vertical-studio"
    });
  }

  ctx.response.body = {
    object: "list",
    data: modelList
  };
});

// 健康检查端点
router.get("/health", (ctx: Context) => {
  ctx.response.body = {
    status: "ok",
    service: "vertical-openai-adapter",
    models: MODELS_DATA.data.length,
    timestamp: getCurrentTimestamp()
  };
});

// 主要聊天完成端点
router.post("/v1/chat/completions", authenticateClient, async (ctx: Context) => {
  const requestId = (ctx as any).requestId || generateUUID().substring(0, 8);

  try {
    logInfo(requestId, "MAIN", "开始处理聊天完成请求");

    const request: ChatCompletionRequest = await ctx.request.body({ type: "json" }).value;

    logInfo(requestId, "MAIN", "解析请求体成功", {
      model: request.model,
      messagesCount: request.messages?.length || 0,
      stream: request.stream,
      temperature: request.temperature,
      max_tokens: request.max_tokens,
      top_p: request.top_p
    });

    // 获取模型配置
    const modelConfig = getModelItem(request.model);
    if (!modelConfig) {
      logError(requestId, "MAIN", "模型未找到", { requestedModel: request.model });
      ctx.response.status = 404;
      ctx.response.body = { detail: `模型 ${request.model} 未找到` };
      return;
    }

    logInfo(requestId, "MAIN", "找到模型配置", {
      modelId: modelConfig.id,
      verticalModelId: modelConfig.vertical_model_id,
      verticalModelUrl: modelConfig.vertical_model_url,
      outputReasoningFlag: modelConfig.output_reasoning_flag
    });

    // 获取 Vertical API 令牌，优先从 X-Vertical-Token 头获取
    let verticalTokenHeader = ctx.request.headers.get('x-vertical-token');
    if (!verticalTokenHeader) {
      // 如果没有 X-Vertical-Token 头，尝试从 Authorization 头提取
      verticalTokenHeader = ctx.request.headers.get('authorization');
    }

    logInfo(requestId, "MAIN", "提取Vertical认证令牌", {
      hasVerticalTokenHeader: !!verticalTokenHeader,
      tokenHeaderPrefix: verticalTokenHeader ? verticalTokenHeader.substring(0, 20) + "..." : null
    });

    const authToken = extractVerticalAuthToken(verticalTokenHeader);
    if (!authToken) {
      logError(requestId, "MAIN", "Vertical API令牌提取失败", {
        verticalTokenHeader: verticalTokenHeader ? verticalTokenHeader.substring(0, 50) + "..." : null
      });
      ctx.response.status = 400;
      ctx.response.body = { detail: "需要在 X-Vertical-Token 头或 Authorization 头中提供 Vertical API 令牌" };
      return;
    }

    logInfo(requestId, "MAIN", "Vertical API令牌提取成功", {
      tokenPrefix: authToken.substring(0, 10) + "..."
    });

    // 从模型配置中提取信息
    const verticalModelId = modelConfig.vertical_model_id;
    const verticalModelUrl = modelConfig.vertical_model_url;
    const outputReasoningActive = modelConfig.output_reasoning_flag || false;

    logInfo(requestId, "CONFIG", "提取模型配置信息", {
      verticalModelId,
      verticalModelUrl,
      outputReasoningActive
    });

    if (!verticalModelId || !verticalModelUrl) {
      logError(requestId, "CONFIG", "模型配置不完整", {
        hasVerticalModelId: !!verticalModelId,
        hasVerticalModelUrl: !!verticalModelUrl
      });
      ctx.response.status = 500;
      ctx.response.body = { detail: "模型配置不完整" };
      return;
    }

    // 提取系统提示和用户消息
    let currentSystemPromptStr = "";
    let latestUserMessageContent = "";
    const messageRoles: string[] = [];

    logInfo(requestId, "MESSAGES", "开始处理消息", {
      totalMessages: request.messages.length
    });

    for (const msg of request.messages) {
      messageRoles.push(msg.role);
      if (msg.role === "system") {
        currentSystemPromptStr += msg.content + "\n";
        logInfo(requestId, "MESSAGES", "处理系统消息", {
          contentLength: msg.content.length,
          contentPreview: msg.content.length > 100 ? msg.content.substring(0, 100) + "..." : msg.content
        });
      } else if (msg.role === "user") {
        latestUserMessageContent = msg.content;
        logInfo(requestId, "MESSAGES", "处理用户消息", {
          contentLength: msg.content.length,
          contentPreview: msg.content.length > 100 ? msg.content.substring(0, 100) + "..." : msg.content
        });
      } else if (msg.role === "assistant") {
        logInfo(requestId, "MESSAGES", "处理助手消息", {
          contentLength: msg.content.length,
          contentPreview: msg.content.length > 100 ? msg.content.substring(0, 100) + "..." : msg.content
        });
      }
    }

    currentSystemPromptStr = currentSystemPromptStr.trim();

    logInfo(requestId, "MESSAGES", "消息处理完成", {
      messageRoles,
      systemPromptLength: currentSystemPromptStr.length,
      latestUserMessageLength: latestUserMessageContent.length,
      hasSystemPrompt: !!currentSystemPromptStr,
      hasUserMessage: !!latestUserMessageContent
    });

    if (!latestUserMessageContent) {
      logError(requestId, "MESSAGES", "未找到用户消息", {
        messageRoles,
        totalMessages: request.messages.length
      });
      ctx.response.status = 400;
      ctx.response.body = { detail: "请求中未找到用户消息" };
      return;
    }

    // 生成系统提示哈希
    logInfo(requestId, "CACHE", "生成系统提示哈希");
    const currentSystemPromptHash = await sha256Hash(currentSystemPromptStr).then(hash => hash.length);

    logInfo(requestId, "CACHE", "系统提示哈希生成完成", {
      hash: currentSystemPromptHash,
      systemPromptLength: currentSystemPromptStr.length
    });

    // 简化的缓存查找（在实际应用中可以实现更复杂的缓存逻辑）
    const isNewCachedConversation = true; // 简化实现
    const matchedConvId = null;

    logInfo(requestId, "CACHE", "缓存查找结果", {
      isNewCachedConversation,
      matchedConvId,
      cacheSize: conversationCache.size
    });

    // 新对话
    logInfo(requestId, "CHAT_ID", "开始创建新对话");
    const newChatId = await verticalApiClient.getChatId(verticalModelUrl, authToken, requestId);

    if (!newChatId) {
      logError(requestId, "CHAT_ID", "无法获取chat_id");
      ctx.response.status = 500;
      ctx.response.body = { detail: "无法从 Vertical API 获取 chat_id" };
      return;
    }

    logInfo(requestId, "CHAT_ID", "成功创建新对话", { chatId: newChatId });

    // 为新对话构造完整历史
    const historyParts: string[] = [];
    logInfo(requestId, "HISTORY", "开始构造消息历史");

    for (const msg of request.messages) {
      if (msg.role === "user") {
        historyParts.push(`User: ${msg.content}`);
      } else if (msg.role === "assistant") {
        historyParts.push(`Assistant: ${msg.content}`);
      }
    }

    const messageToSendToVertical = historyParts.length > 0 ? historyParts.join("\n") : latestUserMessageContent;

    logInfo(requestId, "HISTORY", "消息历史构造完成", {
      historyPartsCount: historyParts.length,
      finalMessageLength: messageToSendToVertical.length,
      messagePreview: messageToSendToVertical.length > 200 ? messageToSendToVertical.substring(0, 200) + "..." : messageToSendToVertical
    });

    // 调用 Vertical API
    logInfo(requestId, "API_CALL", "开始调用Vertical API", {
      chatId: newChatId,
      modelId: verticalModelId,
      outputReasoning: outputReasoningActive,
      messageLength: messageToSendToVertical.length,
      systemPromptLength: currentSystemPromptStr.length
    });

    const apiStreamGenerator = verticalApiClient.sendMessageStream(
      authToken,
      newChatId,
      messageToSendToVertical,
      verticalModelId,
      outputReasoningActive,
      currentSystemPromptStr,
      requestId
    );

    // 创建 OpenAI 格式的流
    logInfo(requestId, "ADAPTER", "创建OpenAI格式流适配器", {
      modelName: request.model,
      reasoningRequested: outputReasoningActive,
      isNewConversation: isNewCachedConversation
    });

    const openaiSseStream = openaiStreamAdapter(
      apiStreamGenerator,
      request.model,
      outputReasoningActive,
      newChatId,
      isNewCachedConversation,
      matchedConvId,
      request.messages,
      currentSystemPromptHash,
      verticalModelUrl,
      requestId
    );

    // 返回流式或非流式响应
    if (request.stream) {
      logInfo(requestId, "RESPONSE", "准备流式响应");

      ctx.response.headers.set("Content-Type", "text/event-stream");
      ctx.response.headers.set("Cache-Control", "no-cache");
      ctx.response.headers.set("Connection", "keep-alive");

      const body = new ReadableStream({
        async start(controller) {
          try {
            let chunkCount = 0;
            for await (const chunk of openaiSseStream) {
              chunkCount++;
              controller.enqueue(new TextEncoder().encode(chunk));
            }
            logInfo(requestId, "RESPONSE", "流式响应完成", { totalChunks: chunkCount });
          } catch (error) {
            logError(requestId, "RESPONSE", "流式响应错误", error);
          } finally {
            controller.close();
            logInfo(requestId, "RESPONSE", "流式响应控制器已关闭");
          }
        }
      });

      ctx.response.body = body;
    } else {
      logInfo(requestId, "RESPONSE", "准备非流式响应");
      const response = await aggregateStreamForNonStreamResponse(openaiSseStream, request.model, requestId);
      logInfo(requestId, "RESPONSE", "非流式响应生成完成", {
        responseId: response.id,
        choicesCount: response.choices.length,
        contentLength: response.choices[0]?.message?.content?.length || 0
      });
      ctx.response.body = response;
    }

    logInfo(requestId, "MAIN", "请求处理完成");

  } catch (error) {
    logError(requestId, "MAIN", "请求处理发生异常", error);
    ctx.response.status = 500;
    ctx.response.body = { detail: `内部服务器错误: ${error.message}` };
  }
});

// ===== 应用程序设置 =====
const app = new Application();

// 错误处理中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  try {
    await next();
  } catch (err) {
    console.error("Application error:", err);
    ctx.response.status = 500;
    ctx.response.body = { detail: "Internal server error" };
  }
});

// CORS 中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  ctx.response.headers.set("Access-Control-Allow-Origin", "*");
  ctx.response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  ctx.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Vertical-Token");

  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 200;
    return;
  }

  await next();
});

// 添加路由
app.use(router.routes());
app.use(router.allowedMethods());

// ===== 启动服务器 =====
async function main() {
  try {
    console.log("🚀 正在启动 Vertical OpenAI Compatible API 服务器...");
    console.log("📋 内置配置:");
    console.log(`   - 可用模型: ${MODELS_DATA.data.length} 个`);
    console.log("");
    console.log("🔗 API 端点:");
    console.log("  GET  /v1/models");
    console.log("  POST /v1/chat/completions");
    console.log("  GET  /health");
    console.log("");
    console.log("🔑 认证说明:");
    console.log("  - Authorization: Bearer <client-api-key>");
    console.log("  - X-Vertical-Token: <vertical-api-token>");
    console.log("");
    console.log(`🌐 服务器运行在 http://localhost:${PORT}`);
    console.log("Vertical OpenAI Compatible API 服务器已启动");

    await app.listen({ port: PORT });
  } catch (error) {
    console.error("❌ 启动服务器时出错:", error);
    // 使用标准退出而不是 Deno.exit
    throw error;
  }
}

// 启动服务器
main().catch(console.error);
